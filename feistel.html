<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>8-bit Binary Feistel Cipher</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            margin: auto;
        }

        h2 {
            color: #1a73e8;
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
        }

        h3 {
            color: #5f6368;
            margin-top: 20px;
        }

        textarea, input {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 2px solid #e8eaed;
            border-radius: 8px;
            box-sizing: border-box;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        textarea:focus, input:focus {
            outline: none;
            border-color: #1a73e8;
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 8px 0;
            width: 100%;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #1557b0;
        }

        button:active {
            background-color: #174ea6;
        }

        .error {
            color: #d93025;
            font-size: 12px;
            margin-top: 4px;
            min-height: 20px;
        }

        #output {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-break: break-all;
            margin-top: 15px;
            border: 1px solid #e8eaed;
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            
            button {
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <h2>8-bit Binary Feistel Cipher</h2>
    
    <textarea id="plaintext" placeholder="Enter binary string (8 bits, only 0s and 1s)" rows="3"></textarea>
    <div id="plaintextError" class="error"></div>
    
    <input type="text" id="key" placeholder="Enter key (4-bit binary, only 0s and 1s)">
    <div id="keyError" class="error"></div>
    
    <button onclick="encrypt()">Encrypt</button>
    <button onclick="decrypt()">Decrypt</button>
    
    <h3>Output:</h3>
    <pre id="output"></pre>
</div>

<script>
    function binaryToDecimal(binary) {
        return parseInt(binary, 2);
    }

    function decimalToBinary(decimal, padding = 4) {
        return decimal.toString(2).padStart(padding, '0');
    }

    function feistelFunction(right, key) {
        // F function for 4-bit blocks
        let rightNum = parseInt(right, 2);
        let keyNum = parseInt(key, 2);
        
        // Circular left shift by 1 and XOR with key
        let shifted = ((rightNum << 1) | (rightNum >> 3)) & 0xF;
        let result = shifted ^ keyNum;
        
        return decimalToBinary(result, 4);
    }

    function feistelRound(left, right, key) {
        let fResult = feistelFunction(right, key);
        let newRight = decimalToBinary(
            parseInt(left, 2) ^ parseInt(fResult, 2),
            4
        );
        return [right, newRight];
    }

    function feistelEncrypt(input, key, rounds = 4) {
        // Split 8-bit input into two 4-bit blocks
        let left = input.slice(0, 4);
        let right = input.slice(4, 8);

        // Apply rounds
        for (let i = 0; i < rounds; i++) {
            [left, right] = feistelRound(left, right, key);
        }

        return [left, right];
    }

    function feistelDecrypt(cipher, key, rounds = 4) {
        let [left, right] = cipher;

        // Apply rounds in reverse
        for (let i = rounds - 1; i >= 0; i--) {
            [right, left] = feistelRound(right, left, key);
        }

        return [left, right];
    }

    function validateBinary(str, length) {
        const binaryRegex = new RegExp(`^[01]{${length}}$`);
        return binaryRegex.test(str);
    }

    function validateInput() {
        const plaintext = document.getElementById("plaintext").value;
        const key = document.getElementById("key").value;
        let isValid = true;

        // Validate plaintext (8 bits)
        if (!validateBinary(plaintext, 8)) {
            document.getElementById("plaintextError").textContent = 
                "Input must be exactly 8 bits (0s and 1s only)";
            isValid = false;
        } else {
            document.getElementById("plaintextError").textContent = "";
        }

        // Validate key (4 bits)
        if (!validateBinary(key, 4)) {
            document.getElementById("keyError").textContent = 
                "Key must be exactly 4 bits (0s and 1s only)";
            isValid = false;
        } else {
            document.getElementById("keyError").textContent = "";
        }

        return isValid;
    }

    let cipherText;

    function encrypt() {
        if (!validateInput()) return;

        const plaintext = document.getElementById("plaintext").value;
        const key = document.getElementById("key").value;

        cipherText = feistelEncrypt(plaintext, key);
        
        document.getElementById("output").textContent = 
            `Original binary:  ${plaintext}\n` +
            `Encrypted binary: ${cipherText.join("")}\n` +
            `Left block (4-bit):  ${cipherText[0]}\n` +
            `Right block (4-bit): ${cipherText[1]}\n` +
            `Round count: 4`;
    }

    function decrypt() {
        if (!cipherText) {
            document.getElementById("output").textContent = "Please encrypt first.";
            return;
        }

        const key = document.getElementById("key").value;
        const decrypted = feistelDecrypt(cipherText, key);
        
        document.getElementById("output").textContent = 
            `Decrypted binary: ${decrypted.join("")}\n` +
            `Left block (4-bit):  ${decrypted[0]}\n` +
            `Right block (4-bit): ${decrypted[1]}`;
    }
</script>
</body>
</html>