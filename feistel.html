<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>8-bit Binary Feistel Cipher</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<div class="container">
    <h2>8-bit Binary Feistel Cipher</h2>

    <textarea id="plaintext" placeholder="Enter binary string (8 bits, only 0s and 1s)" rows="3"></textarea>
    <div id="plaintextError" class="error"></div>

    <input type="text" id="key" placeholder="Enter key (4-bit binary, only 0s and 1s)">
    <div id="keyError" class="error"></div>

    <button onclick="encrypt()">Encrypt</button>
    <button onclick="decrypt()">Decrypt</button>

    <h3>Output:</h3>
    <pre id="output"></pre>
</div>

<script src="script.js"></script>
</body>
</html>