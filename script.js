/* ===== FEISTEL CIPHER JAVASCRIPT =====
 * JavaScript file for the 8-bit Binary Feistel Cipher application
 * Contains all the encryption/decryption logic and user interface functions
 */

// ===== UTILITY FUNCTIONS =====

/**
 * FUNCTION: binaryToDecimal
 * PURPOSE: Converts a binary string to decimal number
 * PARAMETER: binary - string containing only 0s and 1s
 * RETURNS: decimal number equivalent
 * EXAMPLE: binaryToDecimal("1010") returns 10
 */
function binaryToDecimal(binary) {
    return parseInt(binary, 2);
}

/**
 * FUNCTION: decimalToBinary
 * PURPOSE: Converts a decimal number to binary string with padding
 * PARAMETERS: 
 *   - decimal: number to convert
 *   - padding: minimum number of digits (default 4)
 * RETURNS: binary string with leading zeros if needed
 * EXAMPLE: decimalToBinary(5, 4) returns "0101"
 */
function decimalToBinary(decimal, padding = 4) {
    return decimal.toString(2).padStart(padding, '0');
}

// ===== FEISTEL CIPHER CORE FUNCTIONS =====

/**
 * FUNCTION: feistelFunction
 * PURPOSE: The F-function used in each round of Feistel cipher
 * This is the heart of the encryption - it transforms the right block
 * PARAMETERS:
 *   - right: 4-bit binary string (right half of current round)
 *   - key: 4-bit binary string (encryption key)
 * RETURNS: 4-bit binary string (transformed result)
 * PROCESS: 
 *   1. Convert binary to numbers
 *   2. Circular left shift by 1 position
 *   3. XOR with the key
 *   4. Convert back to binary
 */
function feistelFunction(right, key) {
    // Convert binary strings to numbers for mathematical operations
    let rightNum = parseInt(right, 2);
    let keyNum = parseInt(key, 2);
    
    // Circular left shift by 1 position and XOR with key
    // The & 0xF ensures we stay within 4 bits (0-15)
    let shifted = ((rightNum << 1) | (rightNum >> 3)) & 0xF;
    let result = shifted ^ keyNum;
    
    // Convert result back to 4-bit binary string
    return decimalToBinary(result, 4);
}

/**
 * FUNCTION: feistelRound
 * PURPOSE: Performs one round of the Feistel cipher
 * PARAMETERS:
 *   - left: 4-bit binary string (left half)
 *   - right: 4-bit binary string (right half)  
 *   - key: 4-bit binary string (round key)
 * RETURNS: array [newLeft, newRight] for next round
 * PROCESS:
 *   1. Apply F-function to right half with key
 *   2. XOR result with left half to get new right
 *   3. Old right becomes new left
 */
function feistelRound(left, right, key) {
    // Apply the F-function to the right half
    let fResult = feistelFunction(right, key);
    
    // XOR the F-function result with the left half to get new right
    let newRight = decimalToBinary(
        parseInt(left, 2) ^ parseInt(fResult, 2),
        4
    );
    
    // Return [newLeft, newRight] where newLeft = old right
    return [right, newRight];
}

// ===== MAIN ENCRYPTION/DECRYPTION FUNCTIONS =====

/**
 * FUNCTION: feistelEncrypt
 * PURPOSE: Main encryption function - encrypts 8-bit binary input
 * PARAMETERS:
 *   - input: 8-bit binary string to encrypt
 *   - key: 4-bit binary string (encryption key)
 *   - rounds: number of rounds (default 4)
 * RETURNS: array [leftBlock, rightBlock] - the encrypted result
 * PROCESS:
 *   1. Split 8-bit input into two 4-bit halves
 *   2. Apply Feistel rounds for specified number of times
 *   3. Return final left and right blocks
 */
function feistelEncrypt(input, key, rounds = 4) {
    // Split 8-bit input into two 4-bit blocks
    let left = input.slice(0, 4);   // First 4 bits
    let right = input.slice(4, 8);  // Last 4 bits

    // Apply the specified number of Feistel rounds
    for (let i = 0; i < rounds; i++) {
        [left, right] = feistelRound(left, right, key);
    }

    // Return the encrypted blocks
    return [left, right];
}

/**
 * FUNCTION: feistelDecrypt  
 * PURPOSE: Main decryption function - decrypts cipher back to original
 * PARAMETERS:
 *   - cipher: array [leftBlock, rightBlock] from encryption
 *   - key: 4-bit binary string (same key used for encryption)
 *   - rounds: number of rounds (must match encryption rounds)
 * RETURNS: array [leftBlock, rightBlock] - the decrypted result
 * PROCESS:
 *   1. Take the encrypted blocks
 *   2. Apply Feistel rounds in REVERSE order
 *   3. Return decrypted blocks
 */
function feistelDecrypt(cipher, key, rounds = 4) {
    let [left, right] = cipher;

    // Apply rounds in reverse order for decryption
    for (let i = rounds - 1; i >= 0; i--) {
        // Note: we swap the order (right, left) for decryption
        [right, left] = feistelRound(right, left, key);
    }

    return [left, right];
}

// ===== INPUT VALIDATION FUNCTIONS =====

/**
 * FUNCTION: validateBinary
 * PURPOSE: Checks if a string contains only 0s and 1s of exact length
 * PARAMETERS:
 *   - str: string to validate
 *   - length: required exact length
 * RETURNS: true if valid, false if invalid
 * EXAMPLE: validateBinary("1010", 4) returns true
 *          validateBinary("102", 3) returns false (contains '2')
 */
function validateBinary(str, length) {
    const binaryRegex = new RegExp(`^[01]{${length}}$`);
    return binaryRegex.test(str);
}

/**
 * FUNCTION: validateInput
 * PURPOSE: Validates both the binary input and key from the form
 * RETURNS: true if both inputs are valid, false otherwise
 * SIDE EFFECTS: Updates error message displays on the page
 * VALIDATION RULES:
 *   - Binary input must be exactly 8 bits (0s and 1s only)
 *   - Key must be exactly 4 bits (0s and 1s only)
 */
function validateInput() {
    const plaintext = document.getElementById("plaintext").value;
    const key = document.getElementById("key").value;
    let isValid = true;

    // Validate plaintext (must be exactly 8 bits)
    if (!validateBinary(plaintext, 8)) {
        document.getElementById("plaintextError").textContent = 
            "Input must be exactly 8 bits (0s and 1s only)";
        isValid = false;
    } else {
        document.getElementById("plaintextError").textContent = "";
    }

    // Validate key (must be exactly 4 bits)
    if (!validateBinary(key, 4)) {
        document.getElementById("keyError").textContent = 
            "Key must be exactly 4 bits (0s and 1s only)";
        isValid = false;
    } else {
        document.getElementById("keyError").textContent = "";
    }

    return isValid;
}

// ===== GLOBAL VARIABLES =====
// Stores the encrypted result so decrypt function can use it
let cipherText;

// ===== USER INTERFACE FUNCTIONS =====

/**
 * FUNCTION: encrypt
 * PURPOSE: Main function called when "Encrypt" button is clicked
 * PROCESS:
 *   1. Validates user input (binary string and key)
 *   2. Calls feistelEncrypt to encrypt the input
 *   3. Stores result in global cipherText variable
 *   4. Displays encryption results in output area
 * DISPLAYS: Original binary, encrypted binary, left/right blocks, round count
 */
function encrypt() {
    // First validate the input
    if (!validateInput()) return;

    // Get values from the form
    const plaintext = document.getElementById("plaintext").value;
    const key = document.getElementById("key").value;

    // Perform encryption and store result globally
    cipherText = feistelEncrypt(plaintext, key);
    
    // Display the encryption results
    document.getElementById("output").textContent = 
        `Original binary:  ${plaintext}\n` +
        `Encrypted binary: ${cipherText.join("")}\n` +
        `Left block (4-bit):  ${cipherText[0]}\n` +
        `Right block (4-bit): ${cipherText[1]}\n` +
        `Round count: 4`;
}

/**
 * FUNCTION: decrypt
 * PURPOSE: Main function called when "Decrypt" button is clicked
 * REQUIREMENTS: Must call encrypt() first to have cipherText available
 * PROCESS:
 *   1. Checks if encryption was done first
 *   2. Gets the key from form
 *   3. Calls feistelDecrypt to decrypt the stored cipherText
 *   4. Displays decryption results
 * DISPLAYS: Decrypted binary and left/right blocks
 */
function decrypt() {
    // Check if encryption was performed first
    if (!cipherText) {
        document.getElementById("output").textContent = "Please encrypt first.";
        return;
    }

    // Get the key (should be same as used for encryption)
    const key = document.getElementById("key").value;
    
    // Perform decryption
    const decrypted = feistelDecrypt(cipherText, key);
    
    // Display the decryption results
    document.getElementById("output").textContent = 
        `Decrypted binary: ${decrypted.join("")}\n` +
        `Left block (4-bit):  ${decrypted[0]}\n` +
        `Right block (4-bit): ${decrypted[1]}`;
}
