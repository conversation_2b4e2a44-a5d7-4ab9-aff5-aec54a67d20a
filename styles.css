/* ===== FEISTEL CIPHER STYLES - DARK CYBER THEME =====
 * CSS file for the 8-bit Binary Feistel Cipher application
 * Dark cyberpunk-inspired design with neon accents
 */

/* ===== BODY AND LAYOUT STYLES ===== */
/* Main body styling - dark theme with animated background */
body {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    background-attachment: fixed;
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow-x: hidden;
}

/* Animated background effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
    animation: pulse 4s ease-in-out infinite alternate;
    z-index: -1;
}

@keyframes pulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

/* ===== CONTAINER STYLES ===== */
/* Main container with glass morphism effect */
.container {
    background: rgba(20, 20, 40, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 255, 0.3);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 650px;
    margin: auto;
    position: relative;
}

/* Glowing border effect */
.container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ffff);
    border-radius: 22px;
    z-index: -1;
    animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* ===== HEADING STYLES ===== */
/* Main title with neon glow effect */
h2 {
    color: #00ffff;
    margin-top: 0;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2.2em;
    font-weight: bold;
    text-shadow:
        0 0 10px #00ffff,
        0 0 20px #00ffff,
        0 0 30px #00ffff;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff; }
    100% { text-shadow: 0 0 15px #00ffff, 0 0 25px #00ffff, 0 0 35px #00ffff; }
}

/* Section headings with purple glow */
h3 {
    color: #ff00ff;
    margin-top: 25px;
    font-size: 1.3em;
    text-shadow: 0 0 10px #ff00ff;
}

/* ===== INPUT FIELD STYLES ===== */
/* Dark themed input fields with neon glow effects */
textarea, input {
    width: 100%;
    padding: 15px;
    margin: 12px 0;
    background: rgba(10, 10, 30, 0.8);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    box-sizing: border-box;
    font-family: 'Courier New', monospace;
    font-size: 16px;
    color: #00ffff;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

/* Placeholder text styling */
textarea::placeholder, input::placeholder {
    color: rgba(0, 255, 255, 0.5);
    font-style: italic;
}

/* Focus state with enhanced glow effect */
textarea:focus, input:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.5),
        inset 0 0 10px rgba(0, 255, 255, 0.1);
    background: rgba(10, 10, 30, 0.9);
}

/* Specific styling for textarea */
textarea {
    resize: vertical;
    min-height: 100px;
    font-size: 18px;
    line-height: 1.4;
}

/* ===== BUTTON STYLES ===== */
/* Futuristic neon buttons with hover effects */
button {
    background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
    border: 2px solid #00ffff;
    color: #00ffff;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    margin: 10px 0;
    width: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Button glow effect */
button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

/* Button hover effect with enhanced glow */
button:hover {
    background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.5),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

button:hover::before {
    left: 100%;
}

/* Button active effect */
button:active {
    transform: translateY(0);
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.7),
        inset 0 0 15px rgba(0, 255, 255, 0.2);
}

/* ===== ERROR MESSAGE STYLES ===== */
/* Neon red error messages with glow effect */
.error {
    color: #ff3366;
    font-size: 13px;
    margin-top: 6px;
    min-height: 22px;
    text-shadow: 0 0 8px #ff3366;
    font-weight: bold;
    animation: errorPulse 1s ease-in-out infinite alternate;
}

@keyframes errorPulse {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ===== OUTPUT DISPLAY STYLES ===== */
/* Matrix-style output display with scrolling effect */
#output {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 0, 0.5);
    padding: 20px;
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    font-size: 16px;
    color: #00ff00;
    white-space: pre-wrap;
    word-break: break-all;
    margin-top: 20px;
    min-height: 120px;
    box-shadow:
        0 0 20px rgba(0, 255, 0, 0.3),
        inset 0 0 20px rgba(0, 255, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* Matrix rain effect for output */
#output::before {
    content: '';
    position: absolute;
    top: -100%;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, transparent, rgba(0, 255, 0, 0.1), transparent);
    animation: matrixRain 3s linear infinite;
}

@keyframes matrixRain {
    0% { top: -100%; }
    100% { top: 100%; }
}

/* ===== RESPONSIVE DESIGN ===== */
/* Mobile-friendly adjustments for smaller screens */
@media (max-width: 480px) {
    .container {
        padding: 25px;
        margin: 10px;
    }

    button {
        padding: 12px 25px;
        font-size: 14px;
    }

    h2 {
        font-size: 1.8em;
    }

    textarea, input {
        font-size: 14px;
        padding: 12px;
    }
}

/* ===== ADDITIONAL CYBER EFFECTS ===== */
/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
}
