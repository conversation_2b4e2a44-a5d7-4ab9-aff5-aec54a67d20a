/* ===== FEISTEL CIPHER STYLES =====
 * CSS file for the 8-bit Binary Feistel Cipher application
 * Contains all styling for the user interface components
 */

/* ===== BODY AND LAYOUT STYLES ===== */
/* Main body styling - creates centered layout with modern font */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;  /* Light gray background */
    margin: 0;
    padding: 20px;
    min-height: 100vh;  /* Full viewport height */
    display: flex;
    justify-content: center;  /* Center horizontally */
    align-items: center;      /* Center vertically */
}

/* ===== CONTAINER STYLES ===== */
/* Main container that holds all the cipher interface elements */
.container {
    background-color: white;
    padding: 30px;
    border-radius: 12px;  /* Rounded corners */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);  /* Subtle shadow */
    width: 100%;
    max-width: 600px;  /* Maximum width for readability */
    margin: auto;
}

/* ===== HEADING STYLES ===== */
/* Main title styling */
h2 {
    color: #1a73e8;  /* Google blue color */
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
}

/* Section headings */
h3 {
    color: #5f6368;  /* Gray color for secondary headings */
    margin-top: 20px;
}

/* ===== INPUT FIELD STYLES ===== */
/* Styling for text areas and input fields (binary input and key input) */
textarea, input {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    border: 2px solid #e8eaed;  /* Light gray border */
    border-radius: 8px;
    box-sizing: border-box;  /* Include padding in width calculation */
    font-family: 'Courier New', monospace;  /* Monospace font for binary display */
    font-size: 14px;
    transition: border-color 0.3s ease;  /* Smooth border color change */
}

/* Focus state for input fields - highlights when user clicks/tabs into field */
textarea:focus, input:focus {
    outline: none;
    border-color: #1a73e8;  /* Blue border when focused */
}

/* Specific styling for textarea (binary string input area) */
textarea {
    resize: vertical;  /* Allow vertical resizing only */
    min-height: 80px;  /* Minimum height for text area */
}

/* ===== BUTTON STYLES ===== */
/* Styling for Encrypt and Decrypt buttons */
button {
    background-color: #1a73e8;  /* Google blue background */
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;  /* Show pointer cursor on hover */
    font-size: 14px;
    font-weight: 500;  /* Medium font weight */
    margin: 8px 0;
    width: 100%;  /* Full width buttons */
    transition: background-color 0.3s ease;  /* Smooth color transition */
}

/* Button hover effect - darker blue when mouse hovers over */
button:hover {
    background-color: #1557b0;
}

/* Button active effect - even darker when clicked */
button:active {
    background-color: #174ea6;
}

/* ===== ERROR MESSAGE STYLES ===== */
/* Styling for validation error messages */
.error {
    color: #d93025;  /* Red color for errors */
    font-size: 12px;
    margin-top: 4px;
    min-height: 20px;  /* Reserve space even when no error */
}

/* ===== OUTPUT DISPLAY STYLES ===== */
/* Styling for the output area that shows encryption/decryption results */
#output {
    background-color: #f8f9fa;  /* Light gray background */
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;  /* Monospace for binary display */
    font-size: 14px;
    white-space: pre-wrap;  /* Preserve whitespace and line breaks */
    word-break: break-all;  /* Break long binary strings */
    margin-top: 15px;
    border: 1px solid #e8eaed;  /* Light border */
}

/* ===== RESPONSIVE DESIGN ===== */
/* Mobile-friendly adjustments for smaller screens */
@media (max-width: 480px) {
    .container {
        padding: 20px;  /* Reduced padding on mobile */
    }
    
    button {
        padding: 10px 20px;  /* Smaller button padding on mobile */
    }
}
